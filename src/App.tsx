import React, { useState } from 'react';
import { Tabs } from '@mantine/core';
import { ProductShowcase } from './components/ProductShowcase';
import { ProductDetails } from './components/ProductDetails';
import { AnimatePresence } from 'framer-motion';
export function App() {
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null);
  return <div className="w-full min-h-screen bg-gray-50">
      <AnimatePresence mode="wait">
        {selectedProduct ? <ProductDetails key="product-details" productId={selectedProduct} onBack={() => setSelectedProduct(null)} /> : <ProductShowcase key="product-showcase" onSelectProduct={setSelectedProduct} />}
      </AnimatePresence>
    </div>;
}