import React from 'react';
import { Button } from '@mantine/core';
import { motion } from 'framer-motion';
export function Header() {
  return <motion.header initial={{
    y: -100
  }} animate={{
    y: 0
  }} transition={{
    type: 'spring',
    stiffness: 100
  }} className="sticky top-0 z-40 bg-white shadow-md">
      <div className="container mx-auto px-4 py-3 flex items-center justify-between">
        <div className="flex items-center">
          <img src="/Screenshot_2025-07-24_at_7.28.33_PM.png" alt="Mont Drive Logo" className="h-12 mr-2" style={{
          objectFit: 'contain'
        }} />
          <span className="text-gray-500 text-sm italic">Lead Innovation</span>
        </div>
        <div className="flex items-center space-x-4">
          <Button component="a" href="#products" className="bg-red-600 hover:bg-red-700 text-white">
            Our Products
          </Button>
        </div>
      </div>
    </motion.header>;
}