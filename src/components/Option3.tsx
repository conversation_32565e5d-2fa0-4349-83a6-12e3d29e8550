import React from 'react';
import { ChevronRight, ArrowRight, Check, Phone, Mail, MapPin, ChevronDown } from 'lucide-react';
import { Button, Container, Text, Title, Accordion } from '@mantine/core';
export function Option3() {
  return <div className="w-full bg-white">
      {/* Header/Navigation */}
      <header className="sticky top-0 z-40 bg-white shadow-md">
        <div className="container mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center">
            <img src="/Screenshot_2025-07-24_at_7.28.33_PM.png" alt="Mont Drive Logo" className="h-12 mr-2" style={{
            objectFit: 'contain'
          }} />
            <span className="text-gray-500 text-sm italic hidden sm:inline">
              Lead Innovation
            </span>
          </div>
          <nav className="hidden lg:flex space-x-6">
            <a href="#" className="text-gray-800 hover:text-yellow-600 font-medium">
              Home
            </a>
            <a href="#products" className="text-gray-800 hover:text-yellow-600 font-medium">
              Products
            </a>
            <a href="#about" className="text-gray-800 hover:text-yellow-600 font-medium">
              About
            </a>
            <a href="#events" className="text-gray-800 hover:text-yellow-600 font-medium">
              Events
            </a>
            <a href="#gallery" className="text-gray-800 hover:text-yellow-600 font-medium">
              Gallery
            </a>
            <a href="#contact" className="text-gray-800 hover:text-yellow-600 font-medium">
              Contact
            </a>
            <a href="#careers" className="text-gray-800 hover:text-yellow-600 font-medium">
              Careers
            </a>
          </nav>
          <div className="flex items-center space-x-4">
            <Button size="sm" className="hidden md:flex bg-yellow-500 hover:bg-yellow-600 text-white" rightSection={<Phone size={16} />}>
              +91-9999467601
            </Button>
            <button className="lg:hidden">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </header>
      {/* Hero Section */}
      <section className="relative bg-gray-100">
        <div className="container mx-auto px-4 py-16 md:py-24 flex flex-col md:flex-row items-center">
          <div className="md:w-1/2 md:pr-12 mb-10 md:mb-0">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-gray-900">
              <span className="text-yellow-500">Elevating</span> Building
              Technology
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Mont Drive delivers state-of-the-art gearless elevator traction
              machines with unmatched efficiency, reliability, and performance.
            </p>
            <div className="flex flex-wrap gap-4">
              <Button size="lg" className="bg-yellow-500 hover:bg-yellow-600 text-white" rightSection={<ChevronRight size={18} />}>
                Explore Products
              </Button>
              <Button size="lg" variant="outline" className="border-gray-800 text-gray-800 hover:bg-gray-800 hover:text-white">
                Contact Sales
              </Button>
            </div>
          </div>
          <div className="md:w-1/2 relative">
            <div className="absolute inset-0 bg-yellow-200 rounded-full transform -translate-x-4 translate-y-4 -z-10"></div>
            <img src="/Screenshot_2025-07-24_at_7.28.49_PM.png" alt="Gearless Elevator Traction Machine" className="relative z-10 rounded-lg shadow-xl max-w-full h-auto" />
          </div>
        </div>
      </section>
      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="text-yellow-500 font-bold uppercase tracking-wider">
              Why Choose Us
            </span>
            <h2 className="text-3xl font-bold mt-2 mb-4">
              Engineering Excellence
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our gearless elevator traction machines combine innovative design
              with precision engineering for optimal performance.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-gray-50 rounded-xl p-8 transition-all hover:shadow-lg hover:-translate-y-1">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3">Energy Efficient</h3>
              <p className="text-gray-600">
                Our motors are designed for maximum energy efficiency, reducing
                operational costs and environmental impact.
              </p>
            </div>
            <div className="bg-gray-50 rounded-xl p-8 transition-all hover:shadow-lg hover:-translate-y-1">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3">Reliable & Durable</h3>
              <p className="text-gray-600">
                Built to last with premium materials and rigorous quality
                control for years of trouble-free operation.
              </p>
            </div>
            <div className="bg-gray-50 rounded-xl p-8 transition-all hover:shadow-lg hover:-translate-y-1">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-6">
                <svg className="w-8 h-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-3">Advanced Engineering</h3>
              <p className="text-gray-600">
                Cutting-edge design and precision manufacturing for optimal
                performance in all elevator applications.
              </p>
            </div>
          </div>
        </div>
      </section>
      {/* Product Categories Section */}
      <section id="products" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="text-yellow-500 font-bold uppercase tracking-wider">
              Our Range
            </span>
            <h2 className="text-3xl font-bold mt-2 mb-4">Product Categories</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We offer a complete range of gearless elevator traction machines
              for various building requirements.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl overflow-hidden shadow-md transition-transform hover:shadow-xl">
              <div className="h-64 bg-gray-100 flex items-center justify-center p-6">
                <img src="/Screenshot_2025-07-24_at_7.28.40_PM.png" alt="Small Capacity Machines" className="max-h-full w-auto" />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-4">Small Capacity</h3>
                <p className="text-gray-600 mb-6">
                  4-6 Passenger gearless traction machines ideal for residential
                  buildings and small commercial complexes.
                </p>
                <Button className="w-full border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-white" variant="outline" rightSection={<ArrowRight size={16} />}>
                  View Products
                </Button>
              </div>
            </div>
            <div className="bg-white rounded-xl overflow-hidden shadow-md transition-transform hover:shadow-xl">
              <div className="h-64 bg-gray-100 flex items-center justify-center p-6">
                <img src="/Screenshot_2025-07-24_at_7.28.40_PM.png" alt="Medium Capacity Machines" className="max-h-full w-auto" />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-4">Medium Capacity</h3>
                <p className="text-gray-600 mb-6">
                  8-10 Passenger gearless traction machines perfect for mid-size
                  commercial buildings and apartments.
                </p>
                <Button className="w-full border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-white" variant="outline" rightSection={<ArrowRight size={16} />}>
                  View Products
                </Button>
              </div>
            </div>
            <div className="bg-white rounded-xl overflow-hidden shadow-md transition-transform hover:shadow-xl">
              <div className="h-64 bg-gray-100 flex items-center justify-center p-6">
                <img src="/Screenshot_2025-07-24_at_7.28.40_PM.png" alt="Large Capacity Machines" className="max-h-full w-auto" />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-4">Large Capacity</h3>
                <p className="text-gray-600 mb-6">
                  13-15 Passenger gearless traction machines designed for
                  high-rise buildings and heavy traffic areas.
                </p>
                <Button className="w-full border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-white" variant="outline" rightSection={<ArrowRight size={16} />}>
                  View Products
                </Button>
              </div>
            </div>
          </div>
          <div className="text-center mt-12">
            <Button size="lg" className="bg-yellow-500 hover:bg-yellow-600 text-white">
              View All Products
            </Button>
          </div>
        </div>
      </section>
      {/* About Section */}
      <section id="about" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-10 md:mb-0 md:pr-12">
              <span className="text-yellow-500 font-bold uppercase tracking-wider">
                Our Story
              </span>
              <h2 className="text-3xl font-bold mt-2 mb-6">About Mont Drive</h2>
              <p className="text-lg text-gray-700 mb-6">
                Mont Drive is the subsidiary brand name under J.D. Engineering
                Works. We specialize in manufacturing Gearless Elevator Traction
                Machines that combine efficiency, reliability, and cutting-edge
                technology.
              </p>
              <p className="text-lg text-gray-700 mb-6">
                Founded by Mr. Gurdavinder Singh, our company brings forty years
                of engineering experience and a passion for constant improvement
                in energy efficiency.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-yellow-500 font-bold text-xl">
                      40+
                    </span>
                  </div>
                  <div>
                    <p className="font-bold">Years</p>
                    <p className="text-gray-600">Experience</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-yellow-500 font-bold text-xl">6</span>
                  </div>
                  <div>
                    <p className="font-bold">Product</p>
                    <p className="text-gray-600">Models</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-yellow-500 font-bold text-xl">
                      1K+
                    </span>
                  </div>
                  <div>
                    <p className="font-bold">Happy</p>
                    <p className="text-gray-600">Clients</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="md:w-1/2 relative">
              <div className="absolute inset-0 bg-gray-200 rounded-lg transform translate-x-4 translate-y-4 -z-10"></div>
              <img src="/Screenshot_2025-07-24_at_7.28.49_PM.png" alt="Mont Drive Manufacturing Facility" className="relative z-10 rounded-lg shadow-lg w-full h-auto" />
            </div>
          </div>
        </div>
      </section>
      {/* Technical Specs Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="text-yellow-500 font-bold uppercase tracking-wider">
              Technical Excellence
            </span>
            <h2 className="text-3xl font-bold mt-2 mb-4">
              Product Specifications
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our gearless elevator traction machines are designed to meet the
              highest standards of performance and efficiency.
            </p>
          </div>
          <div className="max-w-4xl mx-auto">
            <Accordion>
              <Accordion.Item value="design">
                <Accordion.Control>
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-4">
                      <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </div>
                    <span className="font-bold text-lg">
                      Advanced Design Features
                    </span>
                  </div>
                </Accordion.Control>
                <Accordion.Panel>
                  <ul className="space-y-3 pl-14">
                    <li className="flex items-start">
                      <Check size={18} className="text-yellow-500 mr-2 mt-1" />
                      <span>Permanent Magnet Synchronous Technology</span>
                    </li>
                    <li className="flex items-start">
                      <Check size={18} className="text-yellow-500 mr-2 mt-1" />
                      <span>Compact and Space-Saving Design</span>
                    </li>
                    <li className="flex items-start">
                      <Check size={18} className="text-yellow-500 mr-2 mt-1" />
                      <span>Optimized for Low Noise and Vibration</span>
                    </li>
                    <li className="flex items-start">
                      <Check size={18} className="text-yellow-500 mr-2 mt-1" />
                      <span>Integrated Safety Features</span>
                    </li>
                  </ul>
                </Accordion.Panel>
              </Accordion.Item>
              <Accordion.Item value="efficiency">
                <Accordion.Control>
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-4">
                      <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                    <span className="font-bold text-lg">Energy Efficiency</span>
                  </div>
                </Accordion.Control>
                <Accordion.Panel>
                  <ul className="space-y-3 pl-14">
                    <li className="flex items-start">
                      <Check size={18} className="text-yellow-500 mr-2 mt-1" />
                      <span>
                        Up to 70% Energy Savings Compared to Conventional
                        Systems
                      </span>
                    </li>
                    <li className="flex items-start">
                      <Check size={18} className="text-yellow-500 mr-2 mt-1" />
                      <span>Regenerative Drive Technology</span>
                    </li>
                    <li className="flex items-start">
                      <Check size={18} className="text-yellow-500 mr-2 mt-1" />
                      <span>Optimized Power Consumption During Standby</span>
                    </li>
                    <li className="flex items-start">
                      <Check size={18} className="text-yellow-500 mr-2 mt-1" />
                      <span>Energy Efficiency Class A Rating</span>
                    </li>
                  </ul>
                </Accordion.Panel>
              </Accordion.Item>
              <Accordion.Item value="maintenance">
                <Accordion.Control>
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-4">
                      <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <span className="font-bold text-lg">
                      Maintenance & Reliability
                    </span>
                  </div>
                </Accordion.Control>
                <Accordion.Panel>
                  <ul className="space-y-3 pl-14">
                    <li className="flex items-start">
                      <Check size={18} className="text-yellow-500 mr-2 mt-1" />
                      <span>Minimal Maintenance Requirements</span>
                    </li>
                    <li className="flex items-start">
                      <Check size={18} className="text-yellow-500 mr-2 mt-1" />
                      <span>No Oil or Lubricant Needed</span>
                    </li>
                    <li className="flex items-start">
                      <Check size={18} className="text-yellow-500 mr-2 mt-1" />
                      <span>Extended Service Life</span>
                    </li>
                    <li className="flex items-start">
                      <Check size={18} className="text-yellow-500 mr-2 mt-1" />
                      <span>Comprehensive 5-Year Warranty</span>
                    </li>
                  </ul>
                </Accordion.Panel>
              </Accordion.Item>
            </Accordion>
          </div>
          <div className="text-center mt-12">
            <Button variant="outline" size="lg" className="border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-white">
              Download Technical Specifications
            </Button>
          </div>
        </div>
      </section>
      {/* CTA Section */}
      <section className="py-20 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">
              Ready to Elevate Your Building Projects?
            </h2>
            <p className="text-xl mb-10 text-gray-300">
              Contact our team today to discuss how Mont Drive's gearless
              elevator traction machines can enhance your building's performance
              and efficiency.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Button size="lg" className="bg-yellow-500 hover:bg-yellow-600 text-white px-8">
                Request a Quote
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-gray-900 px-8">
                Download Brochure
              </Button>
            </div>
          </div>
        </div>
      </section>
      {/* Contact Section */}
      <section id="contact" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="text-yellow-500 font-bold uppercase tracking-wider">
              Get in Touch
            </span>
            <h2 className="text-3xl font-bold mt-2 mb-4">Contact Us</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Have questions about our products? Our team is here to help you
              find the perfect elevator solution.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
            <div>
              <div className="bg-gray-50 p-8 rounded-xl mb-8">
                <h3 className="text-2xl font-bold mb-6">Our Offices</h3>
                <div className="mb-6">
                  <h4 className="font-bold text-lg mb-2">Head Office</h4>
                  <div className="flex items-start mb-2">
                    <MapPin size={20} className="text-yellow-500 mr-2 mt-1" />
                    <p>
                      BE- 397A, 3rd Floor, Gali No. 2, Hari Nagar, New Delhi
                      110064
                    </p>
                  </div>
                </div>
                <div className="mb-6">
                  <h4 className="font-bold text-lg mb-2">
                    Manufacturing Plant
                  </h4>
                  <div className="flex items-start mb-2">
                    <MapPin size={20} className="text-yellow-500 mr-2 mt-1" />
                    <p>
                      Plot No. F-217, Riico Industrial Area, Teh-Tapukara,
                      Karoli, Alwar, Rajasthan 301707
                    </p>
                  </div>
                </div>
                <div className="mb-6">
                  <h4 className="font-bold text-lg mb-2">
                    Contact Information
                  </h4>
                  <div className="flex items-center mb-2">
                    <Phone size={20} className="text-yellow-500 mr-2" />
                    <p>+91-9999467601</p>
                  </div>
                  <div className="flex items-center mb-2">
                    <Mail size={20} className="text-yellow-500 mr-2" />
                    <p><EMAIL></p>
                  </div>
                </div>
                <Button variant="outline" className="border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-white">
                  View on Map
                </Button>
              </div>
              <div className="bg-gray-50 p-8 rounded-xl">
                <h3 className="text-2xl font-bold mb-6">Business Hours</h3>
                <ul className="space-y-3">
                  <li className="flex justify-between">
                    <span className="font-medium">Monday - Friday:</span>
                    <span className="text-gray-600">9:00 AM - 6:00 PM</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="font-medium">Saturday:</span>
                    <span className="text-gray-600">9:00 AM - 1:00 PM</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="font-medium">Sunday:</span>
                    <span className="text-gray-600">Closed</span>
                  </li>
                </ul>
              </div>
            </div>
            <div>
              <form className="bg-white p-8 shadow-lg rounded-xl border border-gray-200">
                <h3 className="text-2xl font-bold mb-6">Send us a Message</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label className="block text-gray-700 mb-2" htmlFor="name">
                      Full Name
                    </label>
                    <input type="text" id="name" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent" placeholder="Your name" />
                  </div>
                  <div>
                    <label className="block text-gray-700 mb-2" htmlFor="email">
                      Email Address
                    </label>
                    <input type="email" id="email" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent" placeholder="Your email" />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label className="block text-gray-700 mb-2" htmlFor="phone">
                      Phone Number
                    </label>
                    <input type="tel" id="phone" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent" placeholder="Your phone" />
                  </div>
                  <div>
                    <label className="block text-gray-700 mb-2" htmlFor="subject">
                      Subject
                    </label>
                    <select id="subject" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                      <option value="">Please select</option>
                      <option value="sales">Sales Inquiry</option>
                      <option value="support">Technical Support</option>
                      <option value="partnership">Partnership</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                </div>
                <div className="mb-6">
                  <label className="block text-gray-700 mb-2" htmlFor="message">
                    Message
                  </label>
                  <textarea id="message" rows={4} className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent" placeholder="Your message"></textarea>
                </div>
                <Button type="submit" size="lg" className="w-full bg-yellow-500 hover:bg-yellow-600 text-white">
                  Submit
                </Button>
              </form>
            </div>
          </div>
        </div>
      </section>
      {/* Footer */}
      <footer className="bg-gray-900 text-white pt-16 pb-8">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-10 mb-12">
            <div>
              <img src="/Screenshot_2025-07-24_at_7.28.33_PM.png" alt="Mont Drive Logo" className="h-12 mb-6" style={{
              filter: 'brightness(0) invert(1)'
            }} />
              <p className="text-gray-400 mb-6">
                Leading manufacturer of gearless elevator traction machines,
                committed to innovation and excellence in engineering.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-yellow-500 hover:text-white transition-colors">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" />
                  </svg>
                </a>
                <a href="#" className="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-yellow-500 hover:text-white transition-colors">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" />
                  </svg>
                </a>
                <a href="#" className="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-yellow-500 hover:text-white transition-colors">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                  </svg>
                </a>
                <a href="#" className="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-yellow-500 hover:text-white transition-colors">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </a>
              </div>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-6">Quick Links</h3>
              <ul className="space-y-4">
                <li>
                  <a href="#" className="text-gray-400 hover:text-yellow-500 transition-colors">
                    Home
                  </a>
                </li>
                <li>
                  <a href="#about" className="text-gray-400 hover:text-yellow-500 transition-colors">
                    About Us
                  </a>
                </li>
                <li>
                  <a href="#products" className="text-gray-400 hover:text-yellow-500 transition-colors">
                    Products
                  </a>
                </li>
                <li>
                  <a href="#events" className="text-gray-400 hover:text-yellow-500 transition-colors">
                    Events
                  </a>
                </li>
                <li>
                  <a href="#gallery" className="text-gray-400 hover:text-yellow-500 transition-colors">
                    Gallery
                  </a>
                </li>
                <li>
                  <a href="#contact" className="text-gray-400 hover:text-yellow-500 transition-colors">
                    Contact
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-6">Our Products</h3>
              <ul className="space-y-4">
                <li>
                  <a href="#" className="text-gray-400 hover:text-yellow-500 transition-colors">
                    4 Passenger Machine
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-yellow-500 transition-colors">
                    6 Passenger Machine
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-yellow-500 transition-colors">
                    8 Passenger Machine
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-yellow-500 transition-colors">
                    10 Passenger Machine
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-yellow-500 transition-colors">
                    13 Passenger Machine
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-yellow-500 transition-colors">
                    15 Passenger Machine
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-6">Download</h3>
              <ul className="space-y-4">
                <li>
                  <a href="#" className="flex items-center text-gray-400 hover:text-yellow-500 transition-colors">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clipRule="evenodd" />
                    </svg>
                    Product Brochure
                  </a>
                </li>
                <li>
                  <a href="#" className="flex items-center text-gray-400 hover:text-yellow-500 transition-colors">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clipRule="evenodd" />
                    </svg>
                    Technical Specifications
                  </a>
                </li>
                <li>
                  <a href="#" className="flex items-center text-gray-400 hover:text-yellow-500 transition-colors">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clipRule="evenodd" />
                    </svg>
                    Installation Guide
                  </a>
                </li>
                <li>
                  <a href="#" className="flex items-center text-gray-400 hover:text-yellow-500 transition-colors">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clipRule="evenodd" />
                    </svg>
                    Maintenance Manual
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 mb-4 md:mb-0">
                © 2025 Mont Drive. All rights reserved.
              </p>
              <div className="flex space-x-6">
                <a href="#" className="text-gray-400 hover:text-yellow-500 transition-colors">
                  Privacy Policy
                </a>
                <a href="#" className="text-gray-400 hover:text-yellow-500 transition-colors">
                  Terms & Conditions
                </a>
                <a href="#" className="text-gray-400 hover:text-yellow-500 transition-colors">
                  Sitemap
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
      {/* WhatsApp Contact Button */}
      <a href="#contact" className="fixed bottom-6 right-6 bg-green-500 text-white p-4 rounded-full shadow-lg hover:bg-green-600 transition-colors z-50">
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z" />
        </svg>
      </a>
    </div>;
}