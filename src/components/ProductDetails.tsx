import React from 'react';
import { <PERSON><PERSON>, Tabs, Accordion } from '@mantine/core';
import { ArrowLeft, Download, Check, ChevronDown } from 'lucide-react';
import { motion } from 'framer-motion';
import { Footer } from './Footer';
import { Header } from './Header';
interface ProductSpec {
  modelNo: string;
  dutyLoading: string;
  passengerCapacity: string;
  speed: string;
  sheaveSize: string;
  ropeRatio: string;
  noOfRopes: string;
  ropeType: string;
  current: string;
  power: string;
  rpm: string;
  torque: string;
  frequency: string;
  protection: string;
  startVoltage: string;
  weight: string;
}
const productDetails: Record<string, {
  name: string;
  model: string;
  image: string;
  description: string;
  specs: ProductSpec[];
  features: string[];
  applications: string[];
}> = {
  '4p': {
    name: '4 Passenger Gearless Elevator Machine',
    model: 'JDE 24P 04 Series',
    image: "/Screenshot_2025-07-24_at_7.28.40_PM.png",
    description: 'The JDE 24P 04 Series is our compact gearless traction machine designed specifically for small residential and commercial elevators with a 4-passenger capacity. This model combines energy efficiency with reliable performance in a space-saving design.',
    specs: [{
      modelNo: 'JDE 04D4 A-200',
      dutyLoading: '300',
      passengerCapacity: '4',
      speed: '0.32',
      sheaveSize: '200',
      ropeRatio: '2:1',
      noOfRopes: '4',
      ropeType: '8',
      current: '4.8',
      power: '0.83',
      rpm: '60',
      torque: '100',
      frequency: '12.0',
      protection: 'IP41',
      startVoltage: '180',
      weight: '110'
    }],
    features: ['Compact design ideal for small spaces', 'Energy efficient operation', 'Low noise and vibration', 'Minimal maintenance requirements', 'High reliability and durability', 'Smooth start and stop operation'],
    applications: ['Residential buildings', 'Small commercial buildings', 'Office buildings', 'Low-rise apartments', 'Retail stores']
  },
  '6p': {
    name: '6 Passenger Gearless Elevator Machine',
    model: 'JDE 24P 06 Series',
    image: "/Screenshot_2025-07-24_at_7.28.40_PM.png",
    description: 'The JDE 24P 06 Series is designed for medium-sized residential and commercial elevators with a 6-passenger capacity. This model offers enhanced performance while maintaining energy efficiency.',
    specs: [{
      modelNo: 'JDE 04D4 A-200',
      dutyLoading: '300',
      passengerCapacity: '6',
      speed: '0.50',
      sheaveSize: '200',
      ropeRatio: '2:1',
      noOfRopes: '4',
      ropeType: '8',
      current: '5.0',
      power: '1.00',
      rpm: '95',
      torque: '100',
      frequency: '19.2',
      protection: 'IP41',
      startVoltage: '180',
      weight: '110'
    }],
    features: ['Optimized for medium-sized elevators', 'Energy efficient operation', 'Low noise and vibration', 'Minimal maintenance requirements', 'High reliability and durability', 'Smooth start and stop operation'],
    applications: ['Residential buildings', 'Medium commercial buildings', 'Office buildings', 'Mid-rise apartments', 'Hotels and resorts']
  },
  '8p': {
    name: '8 Passenger Gearless Elevator Machine',
    model: 'JDE 24P 08 Series',
    image: "/Screenshot_2025-07-24_at_7.28.40_PM.png",
    description: 'The JDE 24P 08 Series is our versatile gearless traction machine designed for medium to large residential and commercial elevators with an 8-passenger capacity. This model delivers exceptional performance and reliability.',
    specs: [{
      modelNo: 'JDE 04D4 A-320',
      dutyLoading: '300',
      passengerCapacity: '8',
      speed: '0.80',
      sheaveSize: '200',
      ropeRatio: '2:1',
      noOfRopes: '4',
      ropeType: '8',
      current: '6.5',
      power: '1.80',
      rpm: '110',
      torque: '100',
      frequency: '22.0',
      protection: 'IP41',
      startVoltage: '180',
      weight: '110'
    }],
    features: ['Designed for medium to large elevators', 'High energy efficiency', 'Ultra-low noise operation', 'Minimal maintenance requirements', 'Enhanced reliability and durability', 'Smooth acceleration and deceleration'],
    applications: ['Residential complexes', 'Commercial buildings', 'Office towers', 'Hotels and resorts', 'Shopping malls', 'Hospitals']
  },
  '10p': {
    name: '10 Passenger Gearless Elevator Machine',
    model: 'JDE 24P 10 Series',
    image: "/Screenshot_2025-07-24_at_7.28.40_PM.png",
    description: 'The JDE 24P 10 Series is designed for larger commercial elevators with a 10-passenger capacity. This powerful model combines high performance with energy efficiency for demanding applications.',
    specs: [{
      modelNo: 'JDE 04D4 A-320',
      dutyLoading: '350',
      passengerCapacity: '10',
      speed: '1.00',
      sheaveSize: '240',
      ropeRatio: '2:1',
      noOfRopes: '5',
      ropeType: '10',
      current: '7.2',
      power: '2.20',
      rpm: '120',
      torque: '120',
      frequency: '24.0',
      protection: 'IP41',
      startVoltage: '180',
      weight: '130'
    }],
    features: ['High capacity for commercial applications', 'Superior energy efficiency', 'Ultra-low noise operation', 'Minimal maintenance requirements', 'Maximum reliability and durability', 'Precision floor leveling'],
    applications: ['Commercial buildings', 'Office towers', 'Hotels and resorts', 'Shopping malls', 'Hospitals', 'Educational institutions']
  },
  '13p': {
    name: '13 Passenger Gearless Elevator Machine',
    model: 'JDE 24P 13 Series',
    image: "/Screenshot_2025-07-24_at_7.28.40_PM.png",
    description: 'The JDE 24P 13 Series is our high-capacity gearless traction machine designed for large commercial elevators with a 13-passenger capacity. This model delivers exceptional performance and reliability in demanding environments.',
    specs: [{
      modelNo: 'JDE 04D4 A-400',
      dutyLoading: '400',
      passengerCapacity: '13',
      speed: '1.20',
      sheaveSize: '280',
      ropeRatio: '2:1',
      noOfRopes: '6',
      ropeType: '10',
      current: '8.5',
      power: '3.00',
      rpm: '140',
      torque: '150',
      frequency: '28.0',
      protection: 'IP41',
      startVoltage: '200',
      weight: '160'
    }],
    features: ['High capacity for demanding applications', 'Maximum energy efficiency', 'Ultra-low noise operation', 'Minimal maintenance requirements', 'Superior reliability and durability', 'Precision floor leveling'],
    applications: ['High-rise commercial buildings', 'Office towers', 'Luxury hotels and resorts', 'Shopping malls', 'Hospitals', 'Educational institutions']
  },
  '15p': {
    name: '15 Passenger Gearless Elevator Machine',
    model: 'JDE 24P 15 Series',
    image: "/Screenshot_2025-07-24_at_7.28.40_PM.png",
    description: 'The JDE 24P 15 Series is our largest gearless traction machine designed for high-capacity commercial elevators with a 15-passenger capacity. This model delivers maximum performance and reliability for the most demanding applications.',
    specs: [{
      modelNo: 'JDE 04D4 A-500',
      dutyLoading: '450',
      passengerCapacity: '15',
      speed: '1.60',
      sheaveSize: '320',
      ropeRatio: '2:1',
      noOfRopes: '8',
      ropeType: '12',
      current: '10.2',
      power: '4.00',
      rpm: '160',
      torque: '180',
      frequency: '32.0',
      protection: 'IP41',
      startVoltage: '220',
      weight: '200'
    }],
    features: ['Maximum capacity for high-traffic applications', 'Optimum energy efficiency', 'Ultra-low noise operation', 'Minimal maintenance requirements', 'Superior reliability and durability', 'Precision floor leveling'],
    applications: ['High-rise commercial buildings', 'Skyscrapers', 'Luxury hotels and resorts', 'Shopping malls', 'Hospitals', 'Transit centers']
  }
};
export function ProductDetails({
  productId,
  onBack
}: {
  productId: string;
  onBack: () => void;
}) {
  const product = productDetails[productId];
  if (!product) {
    return <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Product not found</h2>
          <Button onClick={onBack}>Go Back</Button>
        </div>
      </div>;
  }
  return <motion.div initial={{
    opacity: 0
  }} animate={{
    opacity: 1
  }} exit={{
    opacity: 0
  }} className="w-full bg-white">
      <Header />
      {/* Product Hero Section */}
      <section className="bg-gradient-to-r from-gray-900 to-gray-800 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center mb-8">
            <Button variant="subtle" leftSection={<ArrowLeft size={16} />} onClick={onBack} className="text-white hover:bg-gray-800">
              Back to Products
            </Button>
          </div>
          <motion.div initial={{
          y: 20,
          opacity: 0
        }} animate={{
          y: 0,
          opacity: 1
        }} transition={{
          duration: 0.5
        }} className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-8 md:mb-0">
              <span className="inline-block bg-red-600 text-white px-3 py-1 rounded-md text-sm font-medium mb-4">
                {product.model}
              </span>
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
                {product.name}
              </h1>
              <p className="text-lg text-gray-300 mb-6">
                {product.description}
              </p>
              <div className="flex flex-wrap gap-4">
                <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white" rightSection={<Download size={18} />}>
                  Download Specifications
                </Button>
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-gray-900">
                  Request a Quote
                </Button>
              </div>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <motion.div initial={{
              scale: 0.9,
              opacity: 0
            }} animate={{
              scale: 1,
              opacity: 1
            }} transition={{
              duration: 0.5,
              delay: 0.2
            }} className="bg-white p-8 rounded-lg shadow-lg">
                <img src={product.image} alt={product.name} className="max-w-full h-auto" style={{
                maxHeight: '400px'
              }} />
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>
      {/* Product Details Tabs */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <Tabs defaultValue="specifications" className="w-full">
            <Tabs.List className="mb-8 flex justify-center border-b-0">
              <Tabs.Tab value="specifications" className="text-lg font-medium px-6 py-3">
                Technical Specifications
              </Tabs.Tab>
              <Tabs.Tab value="features" className="text-lg font-medium px-6 py-3">
                Features
              </Tabs.Tab>
              <Tabs.Tab value="applications" className="text-lg font-medium px-6 py-3">
                Applications
              </Tabs.Tab>
              <Tabs.Tab value="dimensions" className="text-lg font-medium px-6 py-3">
                Dimensions
              </Tabs.Tab>
            </Tabs.List>
            <Tabs.Panel value="specifications">
              <motion.div initial={{
              opacity: 0
            }} animate={{
              opacity: 1
            }} transition={{
              duration: 0.5
            }}>
                <div className="mb-8 text-center">
                  <h2 className="text-3xl font-bold mb-2">
                    Technical Specifications
                  </h2>
                  <p className="text-gray-600">
                    Voltage: 380 V | Pole: 24 | Duty Cycle: 33-40% | Brake
                    Voltage: 110 VDC | Max. Static Load: 1000 kg
                  </p>
                </div>
                <div className="overflow-x-auto">
                  <table className="min-w-full bg-white border border-gray-200 rounded-lg shadow-md">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="py-3 px-4 text-left font-medium text-gray-700">
                          Model No.
                        </th>
                        <th className="py-3 px-4 text-left font-medium text-gray-700">
                          Duty Loading
                        </th>
                        <th className="py-3 px-4 text-left font-medium text-gray-700">
                          Passenger Capacity
                        </th>
                        <th className="py-3 px-4 text-left font-medium text-gray-700">
                          Speed (m/s)
                        </th>
                        <th className="py-3 px-4 text-left font-medium text-gray-700">
                          Sheave Dia.
                        </th>
                        <th className="py-3 px-4 text-left font-medium text-gray-700">
                          Rope Ratio
                        </th>
                        <th className="py-3 px-4 text-left font-medium text-gray-700">
                          No. of Ropes
                        </th>
                        <th className="py-3 px-4 text-left font-medium text-gray-700">
                          Rope Type (mm)
                        </th>
                        <th className="py-3 px-4 text-left font-medium text-gray-700">
                          Current (A)
                        </th>
                        <th className="py-3 px-4 text-left font-medium text-gray-700">
                          Power (kW)
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {product.specs.map((spec, index) => <tr key={index} className="border-t border-gray-200">
                          <td className="py-3 px-4">{spec.modelNo}</td>
                          <td className="py-3 px-4">{spec.dutyLoading}</td>
                          <td className="py-3 px-4">
                            {spec.passengerCapacity}
                          </td>
                          <td className="py-3 px-4">{spec.speed}</td>
                          <td className="py-3 px-4">{spec.sheaveSize}</td>
                          <td className="py-3 px-4">{spec.ropeRatio}</td>
                          <td className="py-3 px-4">{spec.noOfRopes}</td>
                          <td className="py-3 px-4">{spec.ropeType}</td>
                          <td className="py-3 px-4">{spec.current}</td>
                          <td className="py-3 px-4">{spec.power}</td>
                        </tr>)}
                    </tbody>
                  </table>
                </div>
                <div className="mt-8 overflow-x-auto">
                  <table className="min-w-full bg-white border border-gray-200 rounded-lg shadow-md">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="py-3 px-4 text-left font-medium text-gray-700">
                          RPM
                        </th>
                        <th className="py-3 px-4 text-left font-medium text-gray-700">
                          Torque (Nm)
                        </th>
                        <th className="py-3 px-4 text-left font-medium text-gray-700">
                          Rated Frequency (Hz)
                        </th>
                        <th className="py-3 px-4 text-left font-medium text-gray-700">
                          Protection
                        </th>
                        <th className="py-3 px-4 text-left font-medium text-gray-700">
                          Start Voltage
                        </th>
                        <th className="py-3 px-4 text-left font-medium text-gray-700">
                          Machine Weight (kg)
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {product.specs.map((spec, index) => <tr key={index} className="border-t border-gray-200">
                          <td className="py-3 px-4">{spec.rpm}</td>
                          <td className="py-3 px-4">{spec.torque}</td>
                          <td className="py-3 px-4">{spec.frequency}</td>
                          <td className="py-3 px-4">{spec.protection}</td>
                          <td className="py-3 px-4">{spec.startVoltage}</td>
                          <td className="py-3 px-4">{spec.weight}</td>
                        </tr>)}
                    </tbody>
                  </table>
                </div>
              </motion.div>
            </Tabs.Panel>
            <Tabs.Panel value="features">
              <motion.div initial={{
              opacity: 0
            }} animate={{
              opacity: 1
            }} transition={{
              duration: 0.5
            }} className="max-w-4xl mx-auto">
                <div className="mb-8 text-center">
                  <h2 className="text-3xl font-bold mb-2">Key Features</h2>
                  <p className="text-gray-600">
                    Our {product.name} is designed with advanced features for
                    optimal performance
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {product.features.map((feature, index) => <motion.div key={index} initial={{
                  opacity: 0,
                  y: 20
                }} animate={{
                  opacity: 1,
                  y: 0
                }} transition={{
                  delay: index * 0.1,
                  duration: 0.5
                }} className="flex items-start bg-gray-50 p-6 rounded-lg">
                      <div className="mr-4 bg-red-100 rounded-full p-2">
                        <Check className="h-6 w-6 text-red-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-medium">{feature}</h3>
                      </div>
                    </motion.div>)}
                </div>
              </motion.div>
            </Tabs.Panel>
            <Tabs.Panel value="applications">
              <motion.div initial={{
              opacity: 0
            }} animate={{
              opacity: 1
            }} transition={{
              duration: 0.5
            }} className="max-w-4xl mx-auto">
                <div className="mb-8 text-center">
                  <h2 className="text-3xl font-bold mb-2">
                    Recommended Applications
                  </h2>
                  <p className="text-gray-600">
                    The {product.name} is ideal for the following applications
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {product.applications.map((application, index) => <motion.div key={index} initial={{
                  opacity: 0,
                  scale: 0.9
                }} animate={{
                  opacity: 1,
                  scale: 1
                }} transition={{
                  delay: index * 0.1,
                  duration: 0.5
                }} className="bg-white border border-gray-200 p-6 rounded-lg text-center shadow-md">
                      <h3 className="text-lg font-medium mb-2">
                        {application}
                      </h3>
                    </motion.div>)}
                </div>
              </motion.div>
            </Tabs.Panel>
            <Tabs.Panel value="dimensions">
              <motion.div initial={{
              opacity: 0
            }} animate={{
              opacity: 1
            }} transition={{
              duration: 0.5
            }} className="max-w-5xl mx-auto">
                <div className="mb-8 text-center">
                  <h2 className="text-3xl font-bold mb-2">Dimensions</h2>
                  <p className="text-gray-600">
                    Technical drawings and dimensions of the {product.name}
                  </p>
                </div>
                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-md p-8">
                  <img src="/Screenshot_2025-07-24_at_8.00.43_PM.png" alt="Technical Drawing" className="w-full h-auto" />
                  <p className="text-center mt-4 text-gray-600">
                    All dimensions are in mm
                  </p>
                </div>
              </motion.div>
            </Tabs.Panel>
          </Tabs>
        </div>
      </section>
      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-2">
              Frequently Asked Questions
            </h2>
            <p className="text-gray-600">
              Common questions about the {product.name}
            </p>
          </div>
          <div className="max-w-3xl mx-auto">
            <Accordion>
              <Accordion.Item value="installation">
                <Accordion.Control>
                  <div className="font-medium text-lg">
                    What are the installation requirements?
                  </div>
                </Accordion.Control>
                <Accordion.Panel>
                  <p>
                    The {product.name} requires a standard machine room setup
                    with proper ventilation and power supply. Our technical team
                    will provide detailed installation guidelines and can assist
                    with the installation process.
                  </p>
                </Accordion.Panel>
              </Accordion.Item>
              <Accordion.Item value="maintenance">
                <Accordion.Control>
                  <div className="font-medium text-lg">
                    What maintenance is required?
                  </div>
                </Accordion.Control>
                <Accordion.Panel>
                  <p>
                    Our gearless machines require minimal maintenance compared
                    to traditional systems. We recommend regular inspections
                    every 6 months to ensure optimal performance. The machine
                    does not require oil changes or lubrication of gears,
                    significantly reducing maintenance costs.
                  </p>
                </Accordion.Panel>
              </Accordion.Item>
              <Accordion.Item value="warranty">
                <Accordion.Control>
                  <div className="font-medium text-lg">
                    What warranty is provided?
                  </div>
                </Accordion.Control>
                <Accordion.Panel>
                  <p>
                    The {product.name} comes with a standard 3-year
                    manufacturer's warranty covering all parts and components.
                    Extended warranty options are available for up to 5 years.
                    Our warranty includes technical support and replacement
                    parts.
                  </p>
                </Accordion.Panel>
              </Accordion.Item>
              <Accordion.Item value="energy">
                <Accordion.Control>
                  <div className="font-medium text-lg">
                    How energy efficient is this model?
                  </div>
                </Accordion.Control>
                <Accordion.Panel>
                  <p>
                    The {product.name} is designed for maximum energy
                    efficiency, consuming up to 40% less energy than
                    conventional systems. It features regenerative drive
                    technology that converts excess energy back into the
                    building's electrical system.
                  </p>
                </Accordion.Panel>
              </Accordion.Item>
            </Accordion>
          </div>
        </div>
      </section>
      {/* Related Products */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-2">Related Products</h2>
            <p className="text-gray-600">
              Explore other gearless elevator traction machines in our range
            </p>
          </div>
          <motion.div initial={{
          opacity: 0
        }} animate={{
          opacity: 1
        }} transition={{
          duration: 0.5
        }} className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {Object.entries(productDetails).filter(([id]) => id !== productId).slice(0, 3).map(([id, relatedProduct]) => <motion.div key={id} whileHover={{
            y: -10,
            transition: {
              duration: 0.2
            }
          }} className="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-md">
                  <div className="h-48 bg-gray-100 flex items-center justify-center p-4">
                    <img src={relatedProduct.image} alt={relatedProduct.name} className="max-h-full w-auto" />
                  </div>
                  <div className="p-6">
                    <h3 className="text-lg font-bold mb-2">
                      {relatedProduct.name}
                    </h3>
                    <p className="text-gray-600 mb-4 line-clamp-2">
                      {relatedProduct.description}
                    </p>
                    <Button className="w-full bg-red-600 hover:bg-red-700 text-white" onClick={() => {
                onBack();
                setTimeout(() => document.getElementById('products')?.scrollIntoView({
                  behavior: 'smooth'
                }), 100);
              }}>
                      View Details
                    </Button>
                  </div>
                </motion.div>)}
          </motion.div>
        </div>
      </section>
      {/* CTA Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <motion.div initial={{
          opacity: 0,
          y: 20
        }} whileInView={{
          opacity: 1,
          y: 0
        }} transition={{
          duration: 0.5
        }} viewport={{
          once: true
        }} className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">Ready to Order?</h2>
            <p className="text-xl mb-10 text-gray-300">
              Contact our sales team today to discuss your requirements and get
              a customized quote for the {product.name}.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Button size="lg" className="bg-red-600 hover:bg-red-700 text-white px-8">
                Request a Quote
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-gray-900 px-8">
                Contact Sales
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
      <Footer />
    </motion.div>;
}